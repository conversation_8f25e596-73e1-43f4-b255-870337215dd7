//
//  WheelConfigPopupView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 大转盘配置弹窗组件
 * 支持设置分区数量（4-12个）、每个分区的奖品名称输入、每次消耗积分设置
 */
struct WheelConfigPopupView: View {
    
    @Binding var isPresented: Bool
    let selectedMember: Member?
    let onSave: (WheelConfigData) -> Void
    let onCancel: () -> Void
    
    @State private var sectorCount: Int = 8
    @State private var costPerPlay: String = "10"
    @State private var sectorPrizes: [String] = []
    @State private var showValidationErrors = false
    @State private var validationErrors: [String] = []
    
    private let minSectorCount = 4
    private let maxSectorCount = 12
    
    var body: some View {
        LotteryConfigPopupView(
            isPresented: $isPresented,
            title: "lottery_config.wheel.title".localized,
            onSave: handleSave,
            onCancel: onCancel
        ) {
            VStack(spacing: 20) {
                // 成员信息显示
                if let member = selectedMember {
                    memberInfoSection(member: member)
                }
                
                // 每次消耗积分设置
                ConfigInputField(
                    title: "lottery_config.cost_per_play".localized,
                    placeholder: "lottery_config.cost_per_play.placeholder".localized,
                    text: $costPerPlay,
                    keyboardType: .numberPad
                )
                
                // 分区数量设置
                CountSelectorView(
                    title: "lottery_config.wheel.sector_count".localized,
                    range: "lottery_config.wheel.sector_count.range".localized,
                    count: $sectorCount,
                    minCount: minSectorCount,
                    maxCount: maxSectorCount
                )
                .onChange(of: sectorCount) { newCount in
                    updateSectorPrizes(count: newCount)
                }
                
                // 分区奖品设置
                prizesSection
                
                // 验证错误显示
                if showValidationErrors && !validationErrors.isEmpty {
                    validationErrorsSection
                }
            }
        }
        .onAppear {
            setupInitialData()
        }
        .keyboardToolbar()
        .onChange(of: isPresented) { newValue in
            if newValue {
                // 弹窗打开时重新加载数据
                setupInitialData()
            }
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 成员信息显示区域
     */
    private func memberInfoSection(member: Member) -> some View {
        HStack {
            // 成员头像
            Image(member.avatarImageName)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 40, height: 40)
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 2) {
                Text(member.name ?? "未知成员")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("当前积分：\(member.currentPoints)")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(hex: "#f8ffe5"))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
        )
    }
    
    /**
     * 奖品设置区域
     */
    private var prizesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("lottery_config.prizes".localized)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            LazyVStack(spacing: 8) {
                ForEach(0..<sectorCount, id: \.self) { index in
                    prizeInputRow(index: index)
                }
            }
        }
    }
    
    /**
     * 单个奖品输入行
     */
    private func prizeInputRow(index: Int) -> some View {
        HStack(spacing: 12) {
            // 分区标签
            Text(String(format: "lottery_config.wheel.sector_format".localized, index + 1))
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .frame(width: 60, alignment: .leading)

            // 奖品输入框
            PrizeInputField(
                placeholder: "lottery_config.wheel.sector_prize".localized,
                text: Binding(
                    get: {
                        index < sectorPrizes.count ? sectorPrizes[index] : ""
                    },
                    set: { newValue in
                        if index < sectorPrizes.count {
                            sectorPrizes[index] = newValue
                        }
                    }
                )
            )
            .font(.system(size: 16))
            .foregroundColor(DesignSystem.Colors.textPrimary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.white)
            .cornerRadius(6)
            .overlay(
                RoundedRectangle(cornerRadius: 6)
                    .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 1)
            )
        }
    }
    
    /**
     * 验证错误显示区域
     */
    private var validationErrorsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(validationErrors, id: \.self) { error in
                HStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.red)
                    
                    Text(error)
                        .font(.system(size: 14))
                        .foregroundColor(.red)
                    
                    Spacer()
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.red.opacity(0.1))
        .cornerRadius(8)
    }
    
    // MARK: - 数据处理方法
    
    /**
     * 初始化数据
     */
    private func setupInitialData() {
        loadExistingConfig()
        updateSectorPrizes(count: sectorCount)
    }

    /**
     * 加载现有配置数据
     */
    private func loadExistingConfig() {
        guard let member = selectedMember else { return }

        // 从数据库加载现有配置
        if let existingConfig = DataManager.shared.getWheelConfig(for: member) {
            print("📥 加载现有大转盘配置: 分区数=\(existingConfig.itemCount), 积分=\(existingConfig.costPerPlay)")

            // 更新UI状态
            sectorCount = Int(existingConfig.itemCount)
            costPerPlay = String(existingConfig.costPerPlay)

            // 加载奖品数据
            let sortedItems = existingConfig.allItems
            var loadedPrizes: [String] = []

            for i in 0..<sectorCount {
                if let item = sortedItems.first(where: { $0.itemIndex == i }) {
                    loadedPrizes.append(item.formattedPrizeName)
                } else {
                    loadedPrizes.append("")
                }
            }

            sectorPrizes = loadedPrizes
            print("📥 加载的奖品列表: \(loadedPrizes)")
        } else {
            print("📝 未找到现有配置，使用默认值")
            // 使用默认值
            sectorCount = LotteryConfig.ToolType.wheel.defaultItemCount
            costPerPlay = "10"
        }
    }
    
    /**
     * 更新分区奖品数组
     */
    private func updateSectorPrizes(count: Int) {
        if sectorPrizes.count < count {
            // 增加奖品项
            sectorPrizes.append(contentsOf: Array(repeating: "", count: count - sectorPrizes.count))
        } else if sectorPrizes.count > count {
            // 减少奖品项
            sectorPrizes = Array(sectorPrizes.prefix(count))
        }
    }
    
    /**
     * 验证表单数据
     */
    private func validateForm() -> Bool {
        validationErrors.removeAll()
        
        // 验证积分设置
        if costPerPlay.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            validationErrors.append("lottery_config.validation.invalid_cost".localized)
        } else if let cost = Int(costPerPlay), cost <= 0 {
            validationErrors.append("lottery_config.validation.cost_range".localized)
        } else if Int(costPerPlay) == nil {
            validationErrors.append("lottery_config.validation.invalid_cost".localized)
        }
        
        // 验证奖品名称
        let emptyPrizes = sectorPrizes.enumerated().filter { $0.element.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
        if !emptyPrizes.isEmpty {
            validationErrors.append("lottery_config.validation.empty_prize".localized)
        }
        
        // 验证奖品名称长度
        let longPrizes = sectorPrizes.filter { $0.trimmingCharacters(in: .whitespacesAndNewlines).count > 20 }
        if !longPrizes.isEmpty {
            validationErrors.append("lottery_config.validation.prize_too_long".localized)
        }
        
        showValidationErrors = !validationErrors.isEmpty
        return validationErrors.isEmpty
    }
    
    /**
     * 处理保存操作
     */
    private func handleSave() {
        guard validateForm() else { return }
        
        let configData = WheelConfigData(
            sectorCount: sectorCount,
            costPerPlay: Int(costPerPlay) ?? 0,
            sectorPrizes: sectorPrizes.map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
        )
        
        onSave(configData)
    }
}

/**
 * 大转盘配置数据模型
 */
struct WheelConfigData {
    let sectorCount: Int
    let costPerPlay: Int
    let sectorPrizes: [String]
}

#Preview {
    WheelConfigPopupView(
        isPresented: .constant(true),
        selectedMember: nil,
        onSave: { _ in print("保存大转盘配置") },
        onCancel: { print("取消配置") }
    )
}
